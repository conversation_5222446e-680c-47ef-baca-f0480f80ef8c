<?php

namespace Tests\Feature\Hodor;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\User;
use App\Reservation;
use Carbon\Carbon;

class AnalyticsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);
    }

    /**
     * Test that the main analytics route exists and is accessible.
     */
    public function test_analytics_route_exists(): void
    {
        $response = $this->get(route('hodor.analytics.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('hodor::analytics.index');
    }

    /**
     * Test that the analytics page loads with year parameter.
     */
    public function test_analytics_page_loads_with_year_parameter(): void
    {
        $response = $this->get(route('hodor.analytics.index', ['analytics_year' => '2023']));
        
        $response->assertStatus(200);
        $response->assertViewHas('analytics_year', '2023');
        $response->assertViewHas('analytics_data');
    }

    /**
     * Test that the budget analytics route exists and is accessible.
     */
    public function test_budget_analytics_route_exists(): void
    {
        $response = $this->get(route('hodor.analytics.budget'));
        
        $response->assertStatus(200);
        $response->assertViewIs('hodor::analytics.budget');
    }

    /**
     * Test that the budget analytics page loads with year and month parameters.
     */
    public function test_budget_analytics_page_loads_with_parameters(): void
    {
        $response = $this->get(route('hodor.analytics.budget', [
            'analytics_year' => '2023',
            'analytics_month' => '6'
        ]));
        
        $response->assertStatus(200);
        $response->assertViewHas('analytics_year', '2023');
        $response->assertViewHas('analytics_month', 6);
        $response->assertViewHas('analytics_data');
    }

    /**
     * Test that page titles are generated correctly.
     */
    public function test_page_titles_are_generated_correctly(): void
    {
        // Test main analytics page title
        $response = $this->get(route('hodor.analytics.index', ['analytics_year' => '2023']));
        $response->assertStatus(200);
        $response->assertViewHas('page_title', 'Analytics - 2023');

        // Test budget analytics page title with year and month
        $response = $this->get(route('hodor.analytics.budget', [
            'analytics_year' => '2023',
            'analytics_month' => '6'
        ]));
        $response->assertStatus(200);
        $pageTitle = $response->viewData('page_title');
        $this->assertStringContainsString('Budget analytics', $pageTitle);
        $this->assertStringContainsString('June', $pageTitle);
        $this->assertStringContainsString('2023', $pageTitle);
    }

    /**
     * Test that invalid year parameters are handled gracefully.
     */
    public function test_invalid_year_parameters_handled_gracefully(): void
    {
        // Test with invalid year
        $response = $this->get(route('hodor.analytics.index', ['analytics_year' => 'invalid']));
        $response->assertStatus(200);
        $response->assertViewHas('analytics_year', null);

        // Test with future year
        $futureYear = date('Y') + 10;
        $response = $this->get(route('hodor.analytics.index', ['analytics_year' => $futureYear]));
        $response->assertStatus(200);
        $response->assertViewHas('analytics_year', null);
    }

    /**
     * Test that analytics data structure is correct.
     */
    public function test_analytics_data_structure_is_correct(): void
    {
        $response = $this->get(route('hodor.analytics.index'));
        $response->assertStatus(200);
        
        $analyticsData = $response->viewData('analytics_data');
        
        // Check that required data structure exists
        $this->assertArrayHasKey('counts', $analyticsData);
        $this->assertArrayHasKey('tables', $analyticsData);
        $this->assertArrayHasKey('graphs', $analyticsData);
        
        // Check counts structure
        $this->assertArrayHasKey('total_customers', $analyticsData['counts']);
        $this->assertArrayHasKey('total_reservations', $analyticsData['counts']);
        $this->assertArrayHasKey('location_diff', $analyticsData['counts']);
        
        // Check tables structure
        $this->assertArrayHasKey('pickup_location', $analyticsData['tables']);
        $this->assertArrayHasKey('dropoff_location', $analyticsData['tables']);
        $this->assertArrayHasKey('listing_model', $analyticsData['tables']);
        $this->assertArrayHasKey('listing_group', $analyticsData['tables']);
        
        // Check graphs structure
        $this->assertArrayHasKey('created_at', $analyticsData['graphs']);
        $this->assertArrayHasKey('pickup_date', $analyticsData['graphs']);
        $this->assertArrayHasKey('customers', $analyticsData['graphs']);
    }

    /**
     * Test that year range is calculated correctly.
     */
    public function test_year_range_calculated_correctly(): void
    {
        // Create a reservation in a specific year to establish the range
        Reservation::factory()->create([
            'pickup_date' => '2020-01-15',
            'show' => 'show'
        ]);

        $response = $this->get(route('hodor.analytics.index'));
        $response->assertStatus(200);
        
        $analyticsYears = $response->viewData('analytics_years');
        
        // Should include current year down to 2020
        $currentYear = date('Y');
        $this->assertArrayHasKey($currentYear, $analyticsYears);
        $this->assertArrayHasKey('2020', $analyticsYears);
        
        // Should be in descending order
        $years = array_keys($analyticsYears);
        $this->assertEquals($currentYear, $years[0]);
    }

    /**
     * Test that months data is properly merged for graphs.
     */
    public function test_months_data_properly_merged_for_graphs(): void
    {
        $response = $this->get(route('hodor.analytics.index'));
        $response->assertStatus(200);
        
        $analyticsData = $response->viewData('analytics_data');
        
        // Check that all months are present in graph data
        $expectedMonths = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];
        
        foreach ($expectedMonths as $month) {
            $this->assertArrayHasKey($month, $analyticsData['graphs']['created_at']);
            $this->assertArrayHasKey($month, $analyticsData['graphs']['pickup_date']);
        }
    }
}
