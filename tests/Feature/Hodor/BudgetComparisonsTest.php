<?php

namespace Tests\Feature\Hodor;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\User;
use App\Reservation;
use Carbon\Carbon;

class BudgetComparisonsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);
    }

    /**
     * Test that the budget comparisons route exists and is accessible.
     */
    public function test_budget_comparisons_route_exists(): void
    {
        $response = $this->get(route('hodor.analytics.budget.comparisons'));
        
        $response->assertStatus(200);
        $response->assertViewIs('hodor::analytics.budget_comparisons');
    }

    /**
     * Test that the budget comparisons page loads with default date parameters.
     */
    public function test_budget_comparisons_page_loads_with_default_dates(): void
    {
        $response = $this->get(route('hodor.analytics.budget.comparisons'));
        
        $response->assertStatus(200);
        $response->assertViewHas('date_from');
        $response->assertViewHas('date_to');
        $response->assertViewHas('comparison_from');
        $response->assertViewHas('comparison_to');
        $response->assertViewHas('analytics_data');
    }

    /**
     * Test that the budget comparisons page loads with custom date parameters.
     */
    public function test_budget_comparisons_page_loads_with_custom_dates(): void
    {
        $response = $this->get(route('hodor.analytics.budget.comparisons', [
            'date_from' => '2023-01-01',
            'date_to' => '2023-06-30',
            'comparison_from' => '2022-01-01',
            'comparison_to' => '2022-06-30'
        ]));
        
        $response->assertStatus(200);
        $response->assertViewHas('date_from', '2023-01-01');
        $response->assertViewHas('date_to', '2023-06-30');
        $response->assertViewHas('comparison_from', '2022-01-01');
        $response->assertViewHas('comparison_to', '2022-06-30');
    }

    /**
     * Test that the form submission redirects correctly.
     */
    public function test_budget_comparisons_form_submission_redirects(): void
    {
        $response = $this->post(route('hodor.analytics.budget.comparisons.submit'), [
            'date_from' => '2023-03-01',
            'date_to' => '2023-03-31',
            'comparison_from' => '2022-03-01',
            'comparison_to' => '2022-03-31'
        ]);
        
        $response->assertRedirect(route('hodor.analytics.budget.comparisons', [
            'date_from' => '2023-03-01',
            'date_to' => '2023-03-31',
            'comparison_from' => '2022-03-01',
            'comparison_to' => '2022-03-31'
        ]));
    }

    /**
     * Test that form validation works for required fields.
     */
    public function test_budget_comparisons_form_validation(): void
    {
        $response = $this->post(route('hodor.analytics.budget.comparisons.submit'), [
            // Missing required fields
        ]);
        
        $response->assertSessionHasErrors(['date_from', 'date_to', 'comparison_from', 'comparison_to']);
    }

    /**
     * Test that calculations are performed correctly with test data.
     */
    public function test_budget_comparisons_calculations_with_test_data(): void
    {
        // Create reference period reservations
        Reservation::factory()->create([
            'created_at' => '2023-01-15 10:00:00',
            'final_price' => 1000,
            'total_days' => 5,
            'listing_group' => 'A',
            'show' => 'show'
        ]);
        
        Reservation::factory()->create([
            'created_at' => '2023-01-20 14:00:00',
            'final_price' => 800,
            'total_days' => 4,
            'listing_group' => 'B',
            'show' => 'show'
        ]);

        // Create comparison period reservations
        Reservation::factory()->create([
            'created_at' => '2022-01-15 10:00:00',
            'final_price' => 600,
            'total_days' => 3,
            'listing_group' => 'A',
            'show' => 'show'
        ]);

        $response = $this->get(route('hodor.analytics.budget.comparisons', [
            'date_from' => '2023-01-01',
            'date_to' => '2023-01-31',
            'comparison_from' => '2022-01-01',
            'comparison_to' => '2022-01-31'
        ]));
        
        $response->assertStatus(200);
        
        $analyticsData = $response->viewData('analytics_data');
        
        // Check reference period totals
        $this->assertEquals(2, $analyticsData['counts']['total_reservations_reference']);
        $this->assertEquals(1800, $analyticsData['counts']['total_revenue_reference']);
        $this->assertEquals(9, $analyticsData['counts']['total_days_reference']);
        $this->assertEquals(200, $analyticsData['counts']['avg_revenue_per_day_reference']);
        
        // Check comparison period totals
        $this->assertEquals(1, $analyticsData['counts']['total_reservations_comparison']);
        $this->assertEquals(600, $analyticsData['counts']['total_revenue_comparison']);
        $this->assertEquals(3, $analyticsData['counts']['total_days_comparison']);
        $this->assertEquals(200, $analyticsData['counts']['avg_revenue_per_day_comparison']);
        
        // Check differences
        $this->assertEquals(1, $analyticsData['counts']['diff_reservations']);
        $this->assertEquals(1200, $analyticsData['counts']['diff_revenue']);
        $this->assertEquals(6, $analyticsData['counts']['diff_days']);
        $this->assertEquals(0, $analyticsData['counts']['diff_avg_revenue_per_day']);
        
        // Check percentages
        $this->assertEquals(100, $analyticsData['counts']['pct_reservations']);
        $this->assertEquals(200, $analyticsData['counts']['pct_revenue']);
        $this->assertEquals(200, $analyticsData['counts']['pct_days']);
        $this->assertEquals(0, $analyticsData['counts']['pct_avg_revenue_per_day']);
    }

    /**
     * Test that group-segmented data is calculated correctly.
     */
    public function test_budget_comparisons_group_segmented_data(): void
    {
        // Create reservations for different groups in reference period
        Reservation::factory()->create([
            'created_at' => '2023-02-15 10:00:00',
            'final_price' => 500,
            'total_days' => 2,
            'listing_group' => 'A',
            'show' => 'show'
        ]);
        
        Reservation::factory()->create([
            'created_at' => '2023-02-20 14:00:00',
            'final_price' => 750,
            'total_days' => 3,
            'listing_group' => 'B',
            'show' => 'show'
        ]);

        // Create reservations for comparison period
        Reservation::factory()->create([
            'created_at' => '2022-02-15 10:00:00',
            'final_price' => 400,
            'total_days' => 2,
            'listing_group' => 'A',
            'show' => 'show'
        ]);

        $response = $this->get(route('hodor.analytics.budget.comparisons', [
            'date_from' => '2023-02-01',
            'date_to' => '2023-02-28',
            'comparison_from' => '2022-02-01',
            'comparison_to' => '2022-02-28'
        ]));
        
        $response->assertStatus(200);
        
        $analyticsData = $response->viewData('analytics_data');
        
        // Check group A data
        $this->assertArrayHasKey('A', $analyticsData['groups']['all']);
        $this->assertEquals(1, $analyticsData['groups']['all']['A']['reference']['reservations']);
        $this->assertEquals(500, $analyticsData['groups']['all']['A']['reference']['revenue']);
        $this->assertEquals(1, $analyticsData['groups']['all']['A']['comparison']['reservations']);
        $this->assertEquals(400, $analyticsData['groups']['all']['A']['comparison']['revenue']);
        
        // Check group B data (only in reference period)
        $this->assertArrayHasKey('B', $analyticsData['groups']['all']);
        $this->assertEquals(1, $analyticsData['groups']['all']['B']['reference']['reservations']);
        $this->assertEquals(750, $analyticsData['groups']['all']['B']['reference']['revenue']);
        $this->assertEquals(0, $analyticsData['groups']['all']['B']['comparison']['reservations']);
        $this->assertEquals(0, $analyticsData['groups']['all']['B']['comparison']['revenue']);
    }

    /**
     * Test that no-show reservations are excluded from calculations.
     */
    public function test_budget_comparisons_excludes_no_show_reservations(): void
    {
        // Create show reservation
        Reservation::factory()->create([
            'created_at' => '2023-03-15 10:00:00',
            'final_price' => 400,
            'total_days' => 2,
            'listing_group' => 'A',
            'show' => 'show'
        ]);
        
        // Create no-show reservation
        Reservation::factory()->create([
            'created_at' => '2023-03-20 14:00:00',
            'final_price' => 600,
            'total_days' => 3,
            'listing_group' => 'A',
            'show' => 'no-show'
        ]);

        $response = $this->get(route('hodor.analytics.budget.comparisons', [
            'date_from' => '2023-03-01',
            'date_to' => '2023-03-31',
            'comparison_from' => '2022-03-01',
            'comparison_to' => '2022-03-31'
        ]));
        
        $response->assertStatus(200);
        
        $analyticsData = $response->viewData('analytics_data');
        
        // Only the 'show' reservation should be counted
        $this->assertEquals(1, $analyticsData['counts']['total_reservations_reference']);
        $this->assertEquals(400, $analyticsData['counts']['total_revenue_reference']);
        $this->assertEquals(2, $analyticsData['counts']['total_days_reference']);
    }
}
