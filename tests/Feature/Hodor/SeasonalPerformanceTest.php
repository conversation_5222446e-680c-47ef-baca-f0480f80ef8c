<?php

namespace Tests\Feature\Hodor;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\User;
use App\Reservation;
use Carbon\Carbon;

class SeasonalPerformanceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);
    }

    /**
     * Test that the seasonal performance route exists and is accessible.
     */
    public function test_seasonal_performance_route_exists(): void
    {
        $response = $this->get(route('hodor.analytics.seasonal'));
        
        $response->assertStatus(200);
        $response->assertViewIs('hodor::analytics.seasonal_performance');
    }

    /**
     * Test that the seasonal performance page loads with year parameter.
     */
    public function test_seasonal_performance_page_loads_with_year_parameter(): void
    {
        $response = $this->get(route('hodor.analytics.seasonal', ['analytics_year' => '2023']));
        
        $response->assertStatus(200);
        $response->assertViewHas('analytics_year', '2023');
        $response->assertViewHas('analytics_data');
    }

    /**
     * Test that page title includes the selected year.
     */
    public function test_page_title_includes_year(): void
    {
        $response = $this->get(route('hodor.analytics.seasonal', ['analytics_year' => '2023']));
        
        $response->assertStatus(200);
        $response->assertViewHas('page_title');
        
        $pageTitle = $response->viewData('page_title');
        $this->assertStringContainsString('2023', $pageTitle);
        $this->assertStringContainsString('Seasonal Performance', $pageTitle);
    }

    /**
     * Test seasonal performance calculations with test data.
     */
    public function test_seasonal_performance_calculations_with_test_data(): void
    {
        // Create Winter reservations (Dec 2022, Jan 2023, Feb 2023)
        Reservation::factory()->create([
            'pickup_date' => '2022-12-15',
            'final_price' => 1000,
            'total_days' => 5,
            'listing_group' => 'A',
            'show' => 'show'
        ]);
        
        Reservation::factory()->create([
            'pickup_date' => '2023-01-15',
            'final_price' => 800,
            'total_days' => 4,
            'listing_group' => 'B',
            'show' => 'show'
        ]);
        
        Reservation::factory()->create([
            'pickup_date' => '2023-02-15',
            'final_price' => 600,
            'total_days' => 3,
            'listing_group' => 'A',
            'show' => 'show'
        ]);

        // Create Spring reservations (Mar, Apr, May 2023)
        Reservation::factory()->create([
            'pickup_date' => '2023-03-15',
            'final_price' => 1200,
            'total_days' => 6,
            'listing_group' => 'C',
            'show' => 'show'
        ]);
        
        Reservation::factory()->create([
            'pickup_date' => '2023-04-15',
            'final_price' => 900,
            'total_days' => 5,
            'listing_group' => 'A',
            'show' => 'show'
        ]);

        // Create Summer reservations (Jun, Jul, Aug 2023)
        Reservation::factory()->create([
            'pickup_date' => '2023-06-15',
            'final_price' => 1500,
            'total_days' => 7,
            'listing_group' => 'B',
            'show' => 'show'
        ]);

        // Create Fall reservations (Sep, Oct, Nov 2023)
        Reservation::factory()->create([
            'pickup_date' => '2023-09-15',
            'final_price' => 1100,
            'total_days' => 6,
            'listing_group' => 'A',
            'show' => 'show'
        ]);

        $response = $this->get(route('hodor.analytics.seasonal', ['analytics_year' => '2023']));
        
        $response->assertStatus(200);
        
        $analyticsData = $response->viewData('analytics_data');
        
        // Check Winter season data
        $this->assertEquals(3, $analyticsData['seasons']['Winter']['reservations']);
        $this->assertEquals(2400, $analyticsData['seasons']['Winter']['revenue']); // 1000 + 800 + 600
        $this->assertEquals(12, $analyticsData['seasons']['Winter']['total_days']); // 5 + 4 + 3
        $this->assertEquals(200, $analyticsData['seasons']['Winter']['avg_revenue_per_day']); // 2400 / 12
        
        // Check Spring season data
        $this->assertEquals(2, $analyticsData['seasons']['Spring']['reservations']);
        $this->assertEquals(2100, $analyticsData['seasons']['Spring']['revenue']); // 1200 + 900
        $this->assertEquals(11, $analyticsData['seasons']['Spring']['total_days']); // 6 + 5
        $this->assertEqualsWithDelta(190.91, $analyticsData['seasons']['Spring']['avg_revenue_per_day'], 0.1); // 2100 / 11
        
        // Check Summer season data
        $this->assertEquals(1, $analyticsData['seasons']['Summer']['reservations']);
        $this->assertEquals(1500, $analyticsData['seasons']['Summer']['revenue']);
        $this->assertEquals(7, $analyticsData['seasons']['Summer']['total_days']);
        $this->assertEqualsWithDelta(214.29, $analyticsData['seasons']['Summer']['avg_revenue_per_day'], 0.1); // 1500 / 7
        
        // Check Fall season data
        $this->assertEquals(1, $analyticsData['seasons']['Fall']['reservations']);
        $this->assertEquals(1100, $analyticsData['seasons']['Fall']['revenue']);
        $this->assertEquals(6, $analyticsData['seasons']['Fall']['total_days']);
        $this->assertEqualsWithDelta(183.33, $analyticsData['seasons']['Fall']['avg_revenue_per_day'], 0.1); // 1100 / 6
    }

    /**
     * Test that no-show reservations are excluded from calculations.
     */
    public function test_seasonal_performance_excludes_no_show_reservations(): void
    {
        // Create show reservation
        Reservation::factory()->create([
            'pickup_date' => '2023-03-15',
            'final_price' => 400,
            'total_days' => 2,
            'listing_group' => 'A',
            'show' => 'show'
        ]);
        
        // Create no-show reservation
        Reservation::factory()->create([
            'pickup_date' => '2023-03-20',
            'final_price' => 600,
            'total_days' => 3,
            'listing_group' => 'A',
            'show' => 'no-show'
        ]);

        $response = $this->get(route('hodor.analytics.seasonal', ['analytics_year' => '2023']));
        
        $response->assertStatus(200);
        
        $analyticsData = $response->viewData('analytics_data');
        
        // Only the 'show' reservation should be counted in Spring
        $this->assertEquals(1, $analyticsData['seasons']['Spring']['reservations']);
        $this->assertEquals(400, $analyticsData['seasons']['Spring']['revenue']);
        $this->assertEquals(2, $analyticsData['seasons']['Spring']['total_days']);
    }

    /**
     * Test top performing car groups by season.
     */
    public function test_top_performing_car_groups_by_season(): void
    {
        // Create Spring reservations with different groups
        Reservation::factory()->create([
            'pickup_date' => '2023-03-15',
            'final_price' => 1000,
            'listing_group' => 'A',
            'show' => 'show'
        ]);
        
        Reservation::factory()->create([
            'pickup_date' => '2023-04-15',
            'final_price' => 800,
            'listing_group' => 'B',
            'show' => 'show'
        ]);
        
        Reservation::factory()->create([
            'pickup_date' => '2023-05-15',
            'final_price' => 1200,
            'listing_group' => 'A',
            'show' => 'show'
        ]);
        
        Reservation::factory()->create([
            'pickup_date' => '2023-05-20',
            'final_price' => 600,
            'listing_group' => 'C',
            'show' => 'show'
        ]);

        $response = $this->get(route('hodor.analytics.seasonal', ['analytics_year' => '2023']));
        
        $response->assertStatus(200);
        
        $analyticsData = $response->viewData('analytics_data');
        
        // Check Spring top groups
        $springGroups = $analyticsData['top_groups']['Spring'];
        
        // Should be ordered by revenue (A: 2200, B: 800, C: 600)
        $this->assertEquals('A', $springGroups[0]['listing_group']);
        $this->assertEquals(2200, $springGroups[0]['revenue']);
        $this->assertEquals(2, $springGroups[0]['reservations']);
        
        $this->assertEquals('B', $springGroups[1]['listing_group']);
        $this->assertEquals(800, $springGroups[1]['revenue']);
        $this->assertEquals(1, $springGroups[1]['reservations']);
        
        $this->assertEquals('C', $springGroups[2]['listing_group']);
        $this->assertEquals(600, $springGroups[2]['revenue']);
        $this->assertEquals(1, $springGroups[2]['reservations']);
    }

    /**
     * Test winter season includes December of previous year.
     */
    public function test_winter_season_includes_december_of_previous_year(): void
    {
        // Create December 2022 reservation (should be included in Winter 2023)
        Reservation::factory()->create([
            'pickup_date' => '2022-12-15',
            'final_price' => 500,
            'total_days' => 3,
            'listing_group' => 'A',
            'show' => 'show'
        ]);
        
        // Create January 2023 reservation
        Reservation::factory()->create([
            'pickup_date' => '2023-01-15',
            'final_price' => 600,
            'total_days' => 4,
            'listing_group' => 'B',
            'show' => 'show'
        ]);
        
        // Create December 2023 reservation (should NOT be included in Winter 2023)
        Reservation::factory()->create([
            'pickup_date' => '2023-12-15',
            'final_price' => 700,
            'total_days' => 5,
            'listing_group' => 'C',
            'show' => 'show'
        ]);

        $response = $this->get(route('hodor.analytics.seasonal', ['analytics_year' => '2023']));
        
        $response->assertStatus(200);
        
        $analyticsData = $response->viewData('analytics_data');
        
        // Winter 2023 should include Dec 2022 and Jan 2023, but not Dec 2023
        $this->assertEquals(2, $analyticsData['seasons']['Winter']['reservations']);
        $this->assertEquals(1100, $analyticsData['seasons']['Winter']['revenue']); // 500 + 600
        $this->assertEquals(7, $analyticsData['seasons']['Winter']['total_days']); // 3 + 4
    }

    /**
     * Test that invalid year parameters are handled gracefully.
     */
    public function test_invalid_year_parameters_handled_gracefully(): void
    {
        // Test with invalid year
        $response = $this->get(route('hodor.analytics.seasonal', ['analytics_year' => 'invalid']));
        $response->assertStatus(200);
        $response->assertViewHas('analytics_year', date('Y'));

        // Test with future year
        $futureYear = date('Y') + 10;
        $response = $this->get(route('hodor.analytics.seasonal', ['analytics_year' => $futureYear]));
        $response->assertStatus(200);
        $response->assertViewHas('analytics_year', date('Y'));
    }

    /**
     * Test that analytics data structure is correct.
     */
    public function test_analytics_data_structure_is_correct(): void
    {
        $response = $this->get(route('hodor.analytics.seasonal'));
        $response->assertStatus(200);
        
        $analyticsData = $response->viewData('analytics_data');
        
        // Check that required data structure exists
        $this->assertArrayHasKey('seasons', $analyticsData);
        $this->assertArrayHasKey('top_groups', $analyticsData);
        
        // Check seasons structure
        $expectedSeasons = ['Winter', 'Spring', 'Summer', 'Fall'];
        foreach ($expectedSeasons as $season) {
            $this->assertArrayHasKey($season, $analyticsData['seasons']);
            $this->assertArrayHasKey('reservations', $analyticsData['seasons'][$season]);
            $this->assertArrayHasKey('revenue', $analyticsData['seasons'][$season]);
            $this->assertArrayHasKey('total_days', $analyticsData['seasons'][$season]);
            $this->assertArrayHasKey('avg_revenue_per_day', $analyticsData['seasons'][$season]);
        }
        
        // Check top_groups structure
        foreach ($expectedSeasons as $season) {
            $this->assertArrayHasKey($season, $analyticsData['top_groups']);
            $this->assertIsArray($analyticsData['top_groups'][$season]);
        }
    }

    /**
     * Test that top groups are limited to 5 per season.
     */
    public function test_top_groups_limited_to_five_per_season(): void
    {
        // Create 7 different groups in Spring to test the limit
        $groups = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];
        foreach ($groups as $index => $group) {
            Reservation::factory()->create([
                'pickup_date' => '2023-03-15',
                'final_price' => 1000 - ($index * 100), // Decreasing revenue
                'listing_group' => $group,
                'show' => 'show'
            ]);
        }

        $response = $this->get(route('hodor.analytics.seasonal', ['analytics_year' => '2023']));
        
        $response->assertStatus(200);
        
        $analyticsData = $response->viewData('analytics_data');
        
        // Should only return top 5 groups
        $this->assertCount(5, $analyticsData['top_groups']['Spring']);
        
        // Should be ordered by revenue (highest first)
        $this->assertEquals('A', $analyticsData['top_groups']['Spring'][0]['listing_group']);
        $this->assertEquals('E', $analyticsData['top_groups']['Spring'][4]['listing_group']);
    }
}
