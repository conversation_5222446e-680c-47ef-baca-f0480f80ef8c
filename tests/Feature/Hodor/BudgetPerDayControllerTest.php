<?php

namespace Tests\Feature\Hodor;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\User;
use App\Reservation;
use Carbon\Carbon;
use Modules\Hodor\Http\Controllers\AnalyticsController;
use Modules\Hodor\Http\Requests\BudgetPerDaySearchRequest;
use Illuminate\Http\Request;

class BudgetPerDayControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);
    }

    /**
     * Test that the getBudgetPerDayData method calculates average price per day correctly.
     */
    public function test_get_budget_per_day_data_calculates_average_price_per_day_correctly(): void
    {
        // Create test reservations with known values
        Reservation::factory()->create([
            'pickup_date' => '2023-01-15',
            'final_price' => 1000,
            'total_days' => 5,
            'listing_group' => 'A',
            'show' => 'show'
        ]);

        Reservation::factory()->create([
            'pickup_date' => '2023-01-20',
            'final_price' => 800,
            'total_days' => 4,
            'listing_group' => 'A',
            'show' => 'show'
        ]);

        $controller = new AnalyticsController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('getBudgetPerDayData');
        $method->setAccessible(true);

        $result = $method->invoke($controller, '2023', 'A');

        // Check that January data is calculated correctly
        $this->assertEquals(2, $result['monthly']['January']['reservations']);
        $this->assertEquals(1800, $result['monthly']['January']['revenue']);
        $this->assertEquals(9, $result['monthly']['January']['total_days']);
        $this->assertEquals(200, $result['monthly']['January']['avg_price_per_day']);

        // Check yearly totals
        $this->assertEquals(2, $result['yearly']['reservations']);
        $this->assertEquals(1800, $result['yearly']['revenue']);
        $this->assertEquals(9, $result['yearly']['total_days']);
        $this->assertEquals(200, $result['yearly']['avg_price_per_day']);
    }

    /**
     * Test that the controller handles different year parameters correctly.
     */
    public function test_controller_handles_different_year_parameters(): void
    {
        // Create reservations for different years
        Reservation::factory()->create([
            'pickup_date' => '2022-06-15',
            'final_price' => 500,
            'total_days' => 3,
            'listing_group' => 'B',
            'show' => 'show'
        ]);

        Reservation::factory()->create([
            'pickup_date' => '2023-06-15',
            'final_price' => 600,
            'total_days' => 4,
            'listing_group' => 'B',
            'show' => 'show'
        ]);

        $controller = new AnalyticsController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('getBudgetPerDayData');
        $method->setAccessible(true);

        // Test 2022 data
        $result2022 = $method->invoke($controller, '2022', 'B');
        $this->assertEquals(1, $result2022['yearly']['reservations']);
        $this->assertEquals(500, $result2022['yearly']['revenue']);

        // Test 2023 data
        $result2023 = $method->invoke($controller, '2023', 'B');
        $this->assertEquals(1, $result2023['yearly']['reservations']);
        $this->assertEquals(600, $result2023['yearly']['revenue']);
    }

    /**
     * Test that no-show reservations are excluded from calculations.
     */
    public function test_no_show_reservations_are_excluded(): void
    {
        // Create a show reservation
        Reservation::factory()->create([
            'pickup_date' => '2023-03-15',
            'final_price' => 400,
            'total_days' => 2,
            'listing_group' => 'C',
            'show' => 'show'
        ]);

        // Create a no-show reservation
        Reservation::factory()->create([
            'pickup_date' => '2023-03-20',
            'final_price' => 600,
            'total_days' => 3,
            'listing_group' => 'C',
            'show' => 'no-show'
        ]);

        $controller = new AnalyticsController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('getBudgetPerDayData');
        $method->setAccessible(true);

        $result = $method->invoke($controller, '2023', 'C');

        // Only the 'show' reservation should be counted
        $this->assertEquals(1, $result['yearly']['reservations']);
        $this->assertEquals(400, $result['yearly']['revenue']);
        $this->assertEquals(2, $result['yearly']['total_days']);
    }

    /**
     * Test that group filtering works correctly.
     */
    public function test_group_filtering_works_correctly(): void
    {
        // Create reservations for different groups
        Reservation::factory()->create([
            'pickup_date' => '2023-05-15',
            'final_price' => 300,
            'total_days' => 2,
            'listing_group' => 'A',
            'show' => 'show'
        ]);

        Reservation::factory()->create([
            'pickup_date' => '2023-05-20',
            'final_price' => 400,
            'total_days' => 3,
            'listing_group' => 'B',
            'show' => 'show'
        ]);

        $controller = new AnalyticsController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('getBudgetPerDayData');
        $method->setAccessible(true);

        // Test filtering by group A
        $resultA = $method->invoke($controller, '2023', 'A');
        $this->assertEquals(1, $resultA['yearly']['reservations']);
        $this->assertEquals(300, $resultA['yearly']['revenue']);

        // Test filtering by group B
        $resultB = $method->invoke($controller, '2023', 'B');
        $this->assertEquals(1, $resultB['yearly']['reservations']);
        $this->assertEquals(400, $resultB['yearly']['revenue']);

        // Test no group filter (should include both)
        $resultAll = $method->invoke($controller, '2023', null);
        $this->assertEquals(2, $resultAll['yearly']['reservations']);
        $this->assertEquals(700, $resultAll['yearly']['revenue']);
    }
}
