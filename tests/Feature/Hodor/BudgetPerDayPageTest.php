<?php

namespace Tests\Feature\Hodor;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\User;

class BudgetPerDayPageTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);
    }

    /**
     * Test that the budget per day route exists and is accessible.
     */
    public function test_budget_per_day_route_exists(): void
    {
        $response = $this->get(route('hodor.analytics.budget.perDay'));

        $response->assertStatus(200);
        $response->assertViewIs('hodor::analytics.budget_per_day');
    }

    /**
     * Test that the budget per day page loads with year parameter.
     */
    public function test_budget_per_day_page_loads_with_year_parameter(): void
    {
        $response = $this->get(route('hodor.analytics.budget.perDay', ['analytics_year' => '2023']));

        $response->assertStatus(200);
        $response->assertViewHas('analytics_year', '2023');
        $response->assertViewHas('analytics_data');
    }

    /**
     * Test that the budget per day page loads with group parameter.
     */
    public function test_budget_per_day_page_loads_with_group_parameter(): void
    {
        $response = $this->get(route('hodor.analytics.budget.perDay', [
            'analytics_year' => '2023',
            'group' => 'A'
        ]));

        $response->assertStatus(200);
        $response->assertViewHas('selected_group', 'A');
        $response->assertViewHas('available_groups');
    }

    /**
     * Test that the page title includes the selected year and group.
     */
    public function test_page_title_includes_year_and_group(): void
    {
        $response = $this->get(route('hodor.analytics.budget.perDay', [
            'analytics_year' => '2023',
            'group' => 'B'
        ]));

        $response->assertStatus(200);
        $response->assertViewHas('page_title');

        $pageTitle = $response->viewData('page_title');
        $this->assertStringContainsString('2023', $pageTitle);
        $this->assertStringContainsString('Group B', $pageTitle);
    }
}
