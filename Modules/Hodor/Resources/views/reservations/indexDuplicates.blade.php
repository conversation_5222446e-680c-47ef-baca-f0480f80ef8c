@extends('hodor::layouts.master')

@section('content')

<section class="content">
    <div class="container-fluid">
        <div class="row">
            @include('hodor::common.alert')
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div>{{ $reservations->count() }} of {{ $reservations->total() }} items</div>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Title</th>
                                    <th>Dates</th>
                                    <th>Locations</th>
                                    <th>Customer</th>
                                    <th>Price</th>
                                    <th>Created</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                            @foreach ($reservations as $reservation)
                                <tr>
                                    <td>
                                        <strong><a href="{{ route('hodor.reservations.edit', $reservation->id) }}">{{ $reservation->id }}</a></strong>
                                    </td>
                                    <td><strong><a href="{{ route('hodor.reservations.edit', $reservation->id) }}">{{ $reservation->title }} Group: {{ $reservation->listing_group }}</a></strong></td>
                                    <td><b>{!! $reservation->pickup_date->format('d M Y') !!}</b> {!! substr($reservation->pickup_time, 0, -3) !!}<br>- <b>{!! $reservation->dropoff_date->format('d M Y') !!}</b> {!! substr($reservation->dropoff_time, 0, -3) !!}</td>
                                    <td>{!! $reservation->getPickupLocation() !!} <br>- {!! $reservation->getDropoffLocation() !!}</td>
                                    <td><b>{!! $reservation->getCustomer()->name !!}</b></td>
                                    <td><b>€{!! $reservation->final_price !!}</b></td>
                                    <td>{!! $reservation->created_at->format('d-m-Y H:i') !!}</td>
                                    <td class="text-right project-actions">
                                        <p>
                                            <a target="_blank" class="btn btn-outline-info" title="Edit" href="{{ route('admin.reservations.show', $reservation->id) }}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </p>
                                        {!! Form::open(array('method' => 'put', 'class' => '', 'route' => array('hodor.reservations.unduplicate', $reservation->id))) !!}
                                            {!! Form::token() !!}
                                            <button class="btn btn-outline-warning" title="Mark NOT duplicate anymore" onclick="return confirm('Are you sure you want to mark this as non duplicate anymore?');"><i class="fas fa-eye-dropper"></i></button>
                                        {!! Form::close() !!}
                                        {!! Form::open(array('method' => 'DELETE', 'class' => '', 'route' => array('hodor.reservations.destroy', $reservation->id))) !!}
                                            {!! Form::token() !!}
                                            <button class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this item?');"><i class="fas fa-trash"></i></button>
                                        {!! Form::close() !!}
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div>{{ $reservations->links() }}</div>
</section>
@endsection
