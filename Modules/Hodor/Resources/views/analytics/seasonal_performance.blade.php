@extends('adminlte::page')

@section('title', $page_title)

@section('content_header')
    <h1>{{ $page_title }}</h1>
@stop

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Seasonal Performance Analytics</h3>
                </div>
                <div class="card-body">
                    @include('hodor::analytics._partials.seasonal_form')
                </div>
            </div>
        </div>
    </div>

    @if(isset($analytics_data))
        @include('hodor::analytics._partials.seasonal_summary')
        @include('hodor::analytics._partials.seasonal_charts')
        @include('hodor::analytics._partials.seasonal_top_groups')
    @endif
@endsection

@section('css')
    <style>
        .seasonal-metric {
            text-align: center;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .seasonal-metric h3 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: bold;
        }
        .seasonal-metric h4 {
            margin: 10px 0 5px 0;
            font-size: 1.2rem;
            color: #495057;
        }
        .seasonal-metric p {
            margin: 5px 0 0 0;
            color: #6c757d;
            font-size: 0.9rem;
        }
        .winter-season {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-color: #bbdefb;
        }
        .spring-season {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-color: #c8e6c9;
        }
        .summer-season {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
            border-color: #ffcc02;
        }
        .fall-season {
            background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
            border-color: #f8bbd9;
        }
        .seasonal-table th,
        .seasonal-table td {
            text-align: center;
            vertical-align: middle;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .top-groups-card {
            margin-top: 20px;
        }
        .group-rank {
            font-weight: bold;
            color: #495057;
        }
        .group-revenue {
            color: #28a745;
            font-weight: bold;
        }
    </style>
@stop

@section('js')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Seasonal revenue chart
        const revenueCtx = document.getElementById('seasonalRevenueChart');
        if (revenueCtx) {
            const seasonalData = @json($analytics_data['seasons']);
            const seasons = Object.keys(seasonalData);
            const revenues = seasons.map(season => seasonalData[season].revenue);
            
            new Chart(revenueCtx, {
                type: 'bar',
                data: {
                    labels: seasons,
                    datasets: [{
                        label: 'Revenue',
                        data: revenues,
                        backgroundColor: ['#2196f3', '#4caf50', '#ff9800', '#e91e63'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Seasonal Revenue - {{ $analytics_year }}'
                        }
                    }
                }
            });
        }

        // Seasonal reservations chart
        const reservationsCtx = document.getElementById('seasonalReservationsChart');
        if (reservationsCtx) {
            const seasonalData = @json($analytics_data['seasons']);
            const seasons = Object.keys(seasonalData);
            const reservations = seasons.map(season => seasonalData[season].reservations);
            
            new Chart(reservationsCtx, {
                type: 'doughnut',
                data: {
                    labels: seasons,
                    datasets: [{
                        data: reservations,
                        backgroundColor: ['#2196f3', '#4caf50', '#ff9800', '#e91e63'],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        title: {
                            display: true,
                            text: 'Seasonal Reservations Distribution - {{ $analytics_year }}'
                        }
                    }
                }
            });
        }

        // Average revenue per day chart
        const avgRevenueCtx = document.getElementById('avgRevenueChart');
        if (avgRevenueCtx) {
            const seasonalData = @json($analytics_data['seasons']);
            const seasons = Object.keys(seasonalData);
            const avgRevenues = seasons.map(season => seasonalData[season].avg_revenue_per_day);
            
            new Chart(avgRevenueCtx, {
                type: 'line',
                data: {
                    labels: seasons,
                    datasets: [{
                        label: 'Avg Revenue per Day',
                        data: avgRevenues,
                        borderColor: '#6f42c1',
                        backgroundColor: 'rgba(111, 66, 193, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toFixed(2);
                                }
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Average Revenue per Day by Season - {{ $analytics_year }}'
                        }
                    }
                }
            });
        }
    </script>
@stop
