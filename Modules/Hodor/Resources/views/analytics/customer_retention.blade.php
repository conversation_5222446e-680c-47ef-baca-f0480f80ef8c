@extends('adminlte::page')

@section('title', $page_title)

@section('content_header')
    <h1>{{ $page_title }}</h1>
@stop

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Customer Retention Analytics</h3>
                </div>
                <div class="card-body">
                    @include('hodor::analytics._partials.retention_form')
                </div>
            </div>
        </div>
    </div>

    @if(isset($analytics_data))
        @include('hodor::analytics._partials.retention_summary')
        @include('hodor::analytics._partials.retention_charts')
        @include('hodor::analytics._partials.retention_monthly')
    @endif
@endsection

@section('css')
    <style>
        .retention-metric {
            text-align: center;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
        }
        .retention-metric h3 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: bold;
        }
        .retention-metric p {
            margin: 5px 0 0 0;
            color: #6c757d;
            font-size: 0.9rem;
        }
        .retention-rate {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-color: #c3e6cb;
        }
        .new-customers {
            background: linear-gradient(135deg, #cce5ff 0%, #b3d9ff 100%);
            border-color: #b3d9ff;
        }
        .returning-customers {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-color: #ffeaa7;
        }
        .revenue-split {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-color: #f5c6cb;
        }
        .monthly-table th,
        .monthly-table td {
            text-align: center;
            vertical-align: middle;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
    </style>
@stop

@section('js')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Customer retention chart
        const retentionCtx = document.getElementById('retentionChart');
        if (retentionCtx) {
            new Chart(retentionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['New Customers', 'Returning Customers'],
                    datasets: [{
                        data: [{{ $analytics_data['customer_counts']['new'] }}, {{ $analytics_data['customer_counts']['returning'] }}],
                        backgroundColor: ['#007bff', '#ffc107'],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        title: {
                            display: true,
                            text: 'Customer Distribution - {{ $analytics_year }}'
                        }
                    }
                }
            });
        }

        // Monthly retention rate chart
        const monthlyCtx = document.getElementById('monthlyRetentionChart');
        if (monthlyCtx) {
            const monthlyData = @json($analytics_data['monthly']);
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const retentionRates = [];
            
            for (let i = 1; i <= 12; i++) {
                const monthKey = i.toString().padStart(2, '0');
                retentionRates.push(monthlyData[monthKey] ? monthlyData[monthKey].retention_rate : 0);
            }

            new Chart(monthlyCtx, {
                type: 'line',
                data: {
                    labels: months,
                    datasets: [{
                        label: 'Retention Rate (%)',
                        data: retentionRates,
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Monthly Retention Rate - {{ $analytics_year }}'
                        }
                    }
                }
            });
        }
    </script>
@stop
