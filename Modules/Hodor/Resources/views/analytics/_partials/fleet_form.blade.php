<!-- Fleet Utilization Form -->
<form method="GET" action="{{ $analytics_route }}">
    <div class="row">
        <div class="col-md-3">
            <div class="form-group">
                <label for="analytics_year">Year</label>
                <select name="analytics_year" id="analytics_year" class="form-control">
                    @foreach($analytics_years as $year_key => $year_value)
                        <option value="{{ $year_key }}" {{ $analytics_year == $year_key ? 'selected' : '' }}>
                            {{ $year_value }}
                        </option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label for="analytics_month">Month (Optional)</label>
                <select name="analytics_month" id="analytics_month" class="form-control">
                    <option value="">All Months</option>
                    @foreach($analytics_months as $month_name => $month_value)
                        <option value="{{ $month_value }}" {{ $analytics_month == $month_value ? 'selected' : '' }}>
                            {{ $month_name }}
                        </option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label>&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Analyze Utilization
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>
