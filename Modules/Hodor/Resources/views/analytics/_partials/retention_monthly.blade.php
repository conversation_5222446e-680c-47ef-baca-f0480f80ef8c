<!-- Monthly Retention Data -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Monthly Customer Retention Breakdown - {{ $analytics_year }}</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped monthly-table">
                        <thead>
                            <tr>
                                <th>Month</th>
                                <th>Total Customers</th>
                                <th>New Customers</th>
                                <th>Returning Customers</th>
                                <th>Retention Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                $months = [
                                    '01' => 'January', '02' => 'February', '03' => 'March', '04' => 'April',
                                    '05' => 'May', '06' => 'June', '07' => 'July', '08' => 'August',
                                    '09' => 'September', '10' => 'October', '11' => 'November', '12' => 'December'
                                ];
                            @endphp
                            @foreach($months as $monthKey => $monthName)
                                @php
                                    $monthData = $analytics_data['monthly'][$monthKey] ?? [
                                        'total' => 0,
                                        'new' => 0,
                                        'returning' => 0,
                                        'retention_rate' => 0
                                    ];
                                @endphp
                                <tr>
                                    <td><strong>{{ $monthName }}</strong></td>
                                    <td>{{ number_format($monthData['total']) }}</td>
                                    <td>{{ number_format($monthData['new']) }}</td>
                                    <td>{{ number_format($monthData['returning']) }}</td>
                                    <td>
                                        <span class="badge badge-{{ $monthData['retention_rate'] >= 50 ? 'success' : ($monthData['retention_rate'] >= 25 ? 'warning' : 'danger') }}">
                                            {{ number_format($monthData['retention_rate'], 1) }}%
                                        </span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
