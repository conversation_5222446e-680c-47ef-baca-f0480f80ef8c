<!-- Fleet Utilization Summary -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Fleet Utilization Overview - {{ $analytics_year }}{{ $analytics_month ? ' (' . date('F', mktime(0, 0, 0, $analytics_month, 1)) . ')' : '' }}</h3>
            </div>
            <div class="card-body">
                @php
                    $totalCars = array_sum(array_column($analytics_data['utilization'], 'car_count'));
                    $totalMaxDays = array_sum(array_column($analytics_data['utilization'], 'max_possible_days'));
                    $totalRentalDays = array_sum(array_column($analytics_data['utilization'], 'actual_rental_days'));
                    $overallUtilization = $totalMaxDays > 0 ? ($totalRentalDays / $totalMaxDays) * 100 : 0;
                @endphp
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="utilization-metric">
                            <h3>{{ number_format($totalCars) }}</h3>
                            <p>Total Fleet Size</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="utilization-metric">
                            <h3>{{ number_format($totalRentalDays) }}</h3>
                            <p>Total Rental Days</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="utilization-metric">
                            <h3>{{ number_format($totalMaxDays) }}</h3>
                            <p>Max Possible Days</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="utilization-metric {{ $overallUtilization >= 80 ? 'high-utilization' : ($overallUtilization >= 60 ? 'medium-utilization' : 'low-utilization') }}">
                            <h3>{{ number_format($overallUtilization, 1) }}%</h3>
                            <p>Overall Utilization</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
