@extends('hodor::layouts.master')

@section('content_header')
    <div class="row mb-2">
        <div class="col">
            <h1>{{ $page_title }}</h1>
        </div>
    </div>
    <div class="row mb-2">
        <div class="col">
            @include('hodor::analytics._partials.comparison_form')
        </div>
    </div>
@stop

@section('content')
    @include('hodor::analytics._partials.comparison_counts')
    @include('hodor::analytics._partials.comparison_groups')
{{--    @include('hodor::analytics._partials.analytics_graphs', ['selectedDates' => 'Pickup'])--}}
{{--    @include('hodor::analytics._partials.analytics_graphs', ['selectedDates' => 'Created'])--}}
{{--    @include('hodor::analytics._partials.tables')--}}
@endsection

@section('css')
    <link rel="stylesheet" href="/css/jquery-ui.css">
    <style>
        .text-success {
            color: #28a745 !important;
        }
        .text-danger {
            color: #dc3545 !important;
        }
        .comparison-table {
            white-space: nowrap;
        }
        .comparison-table th,
        .comparison-table td {
            text-align: center;
            vertical-align: middle;
            min-width: 120px;
        }
        .comparison-table .group-name {
            text-align: left;
            font-weight: bold;
            min-width: 80px;
        }
        /* Ensure UI elements stay in single row */
        .table-responsive {
            overflow-x: auto;
        }
        .ui-datepicker-calendar .ui-state-active {
            color: rgb(51,51,51) !important;
        }
        /* Datepicker styling */
        .ui-datepicker {
            z-index: 1051 !important;
        }
    </style>
@stop

@section('js')
    <script>
{{--        let tableData={!! json_encode($analytics_data['tables']) !!};--}}
{{--        let graphData={!! json_encode($analytics_data['graphs']) !!};--}}
    </script>
    <script src="/js/jquery-ui.js"></script>
    <script src="/js/admin_analytics.js?v={!! env('APP_SCRIPTS_VERSION', '1') !!}"></script>
    <script>
        $(function() {
            // Initialize all datepickers with enhanced options
            $('#date_from_picker, #date_to_picker, #comparison_from_picker, #comparison_to_picker').datepicker({
                dateFormat: 'yy-mm-dd',
                changeMonth: true,
                changeYear: true,
                showOtherMonths: true,
                selectOtherMonths: true,
                showButtonPanel: true,
                yearRange: '2010:+0',
                beforeShow: function(input, inst) {
                    // Ensure the datepicker appears above other elements
                    setTimeout(function() {
                        inst.dpDiv.css({
                            'z-index': 1051 // Higher than modal (1050)
                        });
                    }, 0);
                },
                // Override the _updateDatepicker method to fix text color issues
                beforeShowDay: function(date) {
                    return [true, ''];
                },
                onSelect: function(dateText, inst) {
                    // Force color update after selection
                    setTimeout(function() {
                        $('.ui-datepicker-calendar .ui-state-active').css('color', 'rgb(51,51,51)');
                    }, 0);
                }
            });

            // Add calendar icon to the input fields
            $('#date_from_picker, #date_to_picker, #comparison_from_picker, #comparison_to_picker').addClass('pl-2').css({
                'background-image': 'url("/vendor/adminlte/dist/img/calendar.png")',
                'background-position': 'right 10px center',
                'background-repeat': 'no-repeat',
                'background-size': '20px',
                'padding-right': '40px',
                'cursor': 'pointer'
            });
        });
    </script>
@stop
