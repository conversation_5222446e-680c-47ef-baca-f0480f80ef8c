@extends('adminlte::page')

@section('title', $page_title)

@section('content_header')
    <h1>{{ $page_title }}</h1>
@stop

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Fleet Utilization Analytics</h3>
                </div>
                <div class="card-body">
                    @include('hodor::analytics._partials.fleet_form')
                </div>
            </div>
        </div>
    </div>

    @if(isset($analytics_data))
        @include('hodor::analytics._partials.fleet_summary')
        @include('hodor::analytics._partials.fleet_utilization_table')
        @if(isset($analytics_data['monthly']))
            @include('hodor::analytics._partials.fleet_monthly_chart')
        @endif
    @endif
@endsection

@section('css')
    <style>
        .utilization-metric {
            text-align: center;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
        }
        .utilization-metric h3 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: bold;
        }
        .utilization-metric p {
            margin: 5px 0 0 0;
            color: #6c757d;
            font-size: 0.9rem;
        }
        .high-utilization {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-color: #c3e6cb;
        }
        .medium-utilization {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-color: #ffeaa7;
        }
        .low-utilization {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-color: #f5c6cb;
        }
        .utilization-table th,
        .utilization-table td {
            text-align: center;
            vertical-align: middle;
        }
        .utilization-bar {
            height: 20px;
            border-radius: 10px;
            background: linear-gradient(90deg, #e9ecef 0%, #e9ecef 100%);
            position: relative;
            overflow: hidden;
        }
        .utilization-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
    </style>
@stop

@section('js')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Fleet utilization by group chart
        const utilizationCtx = document.getElementById('utilizationChart');
        if (utilizationCtx) {
            const utilizationData = @json($analytics_data['utilization']);
            const groups = Object.keys(utilizationData);
            const rates = groups.map(group => utilizationData[group].utilization_rate);
            
            new Chart(utilizationCtx, {
                type: 'bar',
                data: {
                    labels: groups,
                    datasets: [{
                        label: 'Utilization Rate (%)',
                        data: rates,
                        backgroundColor: rates.map(rate => {
                            if (rate >= 80) return '#28a745';
                            if (rate >= 60) return '#ffc107';
                            return '#dc3545';
                        }),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Fleet Utilization by Car Group'
                        }
                    }
                }
            });
        }

        @if(isset($analytics_data['monthly']))
        // Monthly utilization chart
        const monthlyCtx = document.getElementById('monthlyUtilizationChart');
        if (monthlyCtx) {
            const monthlyData = @json($analytics_data['monthly']);
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const utilizationRates = [];
            
            for (let i = 1; i <= 12; i++) {
                const monthKey = i.toString().padStart(2, '0');
                utilizationRates.push(monthlyData[monthKey] ? monthlyData[monthKey].utilization_rate : 0);
            }

            new Chart(monthlyCtx, {
                type: 'line',
                data: {
                    labels: months,
                    datasets: [{
                        label: 'Fleet Utilization (%)',
                        data: utilizationRates,
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Monthly Fleet Utilization - {{ $analytics_year }}'
                        }
                    }
                }
            });
        }
        @endif
    </script>
@stop
