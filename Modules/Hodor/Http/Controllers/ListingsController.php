<?php

namespace Modules\Hodor\Http\Controllers;

use App\Category;
use App\Group;
use App\Listing;
use App\Motif;
use App\Services\Search\ListingSearch;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Modules\Hodor\Http\Requests\ListingPhotoStoreRequest;
use Modules\Hodor\Http\Requests\ListingStoreRequest;
use Modules\Hodor\Http\Requests\ListingTextUpdateRequest;
use Modules\Hodor\Http\Requests\ListingUpdateRequest;
use Modules\Hodor\Http\Requests\ListingSearchRequest;

class ListingsController extends HodorController
{
    /**
     * @var $languages
     */
    private $languages;

    public function __construct()
    {
        // languages
        foreach (config('laravellocalization.supportedLocales') as $langKey => $langData){
            $this->languages[$langKey] = $langData['name'];
        }
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(ListingSearchRequest $request)
    {
        $this->view_data['listings']    = ListingSearch::apply($request);
        $this->view_data['page_title']  = 'Cars';
        $this->view_data['languages']   = $this->languages;
        $this->view_data['groups']      = Group::pluck('name', 'id')->all();
        $this->view_data['motifs']      = Motif::pluck('title', 'id')->all();
//        $this->view_data['categories']  = array('' => 'Category') + Category::all()->pluck('name', 'id')->all();

        return view('hodor::listings.index', $this->view_data);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        $this->view_data['page_title']  = 'Create New Car';
        $this->view_data['groups']      = Group::pluck('name', 'id')->all();
//        $this->view_data['languages'] = $this->languages;

        return view('hodor::listings.create', $this->view_data);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(ListingStoreRequest $request)
    {
        $listing = Listing::create($request->validated());

        return redirect()->route('hodor.listings.edit',  $listing->id)
            ->withSuccess('Entity with id: ' . $listing->id . ' was successfully created!');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit(int $id)
    {
        $listing = Listing::findOrFail($id);

        $this->view_data['listing']     = $listing;
        $this->view_data['page_title']  = $listing->title;
        $this->view_data['groups']      = Group::pluck('name', 'id');
        $this->view_data['categories']  = Category::pluck('name', 'id');
        $this->view_data['motifs']      = Motif::pluck('title', 'id');

        return view('hodor::listings.edit', $this->view_data);
    }

    /**
     * Update the specified resource in storage.
     * @param ListingUpdateRequest $request
     * @param int $id
     * @return Renderable
     */
    public function update(ListingUpdateRequest $request, $id)
    {
        $listing = Listing::findOrFail($id);

        $listing->update($request->validated());

        // hack to bypass the issue of updating the updated_at filed of the post on translation change
        $listing->touch();

        // sync categories
        $listing->categories()->sync($request->input('categories'));

        // sync motifs
        $listing->motifs()->sync($request->input('motifs'));

        return redirect()->route('hodor.listings.edit',  $listing->id)
            ->withSuccess('Entity with id: ' . $listing->id . ' was successfully updated!');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function textEdit(int $id)
    {
        $listing = Listing::findOrFail($id);

        $this->view_data['listing']     = $listing;
        $this->view_data['page_title']  = $listing->title . ': Edit texts';

        // languages
        $this->view_data['languages'] = $this->languages;

        return view('hodor::listings.edit_texts', $this->view_data);
    }

    /**
     * Update the specified resource in storage.
     * @param ListingTextUpdateRequest $request
     * @param int $id
     * @return Renderable
     */
    public function textUpdate(ListingTextUpdateRequest $request, $id)
    {
        $listing = Listing::findOrFail($id);

        $listing->update($request->validated());

        // hack to bypass the issue of updating the updated_at filed of the post on translation change
        $listing->touch();

        return redirect()->route('hodor.listings.texts.edit',  $listing->id)
            ->withSuccess('The texts of entity with id: ' . $listing->id . ' successfully updated!');
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        $deleteItem =  Listing::find($id);

        if ($deleteItem->delete()) {
            return redirect()->route('hodor.listings.index')
                ->withSuccess('Entity with id: ' . $id . ' was successfully deleted!');
        } else {
            return redirect()->route('hodor.listings.index')
                ->withError('msg', 'An error occured!');
        }
    }

    /**
     * Show the form for previewing the photos of the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function photosIndex(int $listing_id)
    {
        $listing = Listing::findOrFail($listing_id);

        $this->view_data['listing']     = $listing;
        $this->view_data['photos']      = $listing->getMedia('photos');
        $this->view_data['page_title']  = $listing->title . ': Photo Management';

        // languages
        $this->view_data['languages'] = $this->languages;

        $this->view_data['upload_max_filesize'] = $this->return_bytes(ini_get('upload_max_filesize'));
        $this->view_data['upload_max_filesize_short'] = ini_get('upload_max_filesize');

        return view('hodor::listings.index_photos', $this->view_data);
    }


    /**
     * Update the specified resource in storage.
     * @param ListingPhotoStoreRequest $request
     * @param int $id
     * @return Renderable
     */
    public function photoStore(ListingPhotoStoreRequest $request, $listing_id)
    {
        $listing = Listing::findOrFail($listing_id);

        $listing->update($request->validated());

        // hack to bypass the issue of updating the updated_at filed of the model on translation change
        $listing->touch();

        // handle photo data
        if ($request->hasFile('photo') && $request->file('photo')->isValid())
        {
            $listing->storePhoto('img/listings/');
        }

        return redirect()->route('hodor.listings.photos.index',  $listing->id)
            ->withSuccess('Entity with id: ' . $listing->id . ' was successfully updated!');
    }


    /**
     * Convert byte value from shorthand byte notation
     * @param string $val
     */
    private function return_bytes($val) {
        $val = trim($val);
        $last = strtolower($val[strlen($val)-1]);
        $val = (int)$val;
        switch($last) {
            // The 'G' modifier is available since PHP 5.1.0
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }

        return $val;
    }


    // tmp mefod to convert images to media
    public function makeMedia()
    {
        // mass creation
        $listings = Listing::all();

        foreach($listings as $listing)
        {
            $filename = Config::get('images.full_size') . $listing->images->first()->filename;

            if(file_exists($filename))
            {
                $listing->addMedia($filename)
                    ->preservingOriginal()
                    ->withResponsiveImages()
                    ->toMediaCollection('photos');

                Log::info('Listing with id: ' . $listing->id . ' had its media created');
            }
            else
            {
                Log::error('An erro occured when migratin listing with id: ' . $listing->id . ' to media');
            }
        }

        return true;
    }

}
