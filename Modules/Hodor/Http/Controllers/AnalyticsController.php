<?php

namespace Modules\Hodor\Http\Controllers;

use App\Reservation;
use Carbon\Carbon;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Modules\Hodor\Http\Requests\AnalyticsSearchRequest;
use Modules\Hodor\Http\Requests\BudgetAnalyticsSearchRequest;
use Modules\Hodor\Http\Requests\BudgetComparisonsPostRequest;
use Modules\Hodor\Http\Requests\BudgetComparisonsSearchRequest;
use Modules\Hodor\Http\Requests\BudgetPerDaySearchRequest;

class AnalyticsController extends HodorController
{
    /**
     * Display a listing of the resource.
     *
     * @return Renderable
     */
    public function index(AnalyticsSearchRequest $request)
    {
        // Get the analytics range data
        $start_year = Reservation::selectRaw('date_format(min(pickup_date), "%Y") as date')->first()->date;
        $year_range = range(date('Y'), $start_year);
        $analytics_year = null;
        try {
            $analytics_year = Carbon::createFromFormat('Y', $request->analytics_year)->format('Y');
            $analytics_year = in_array($analytics_year, $year_range) ? $analytics_year : null;
        } catch (\Exception $e)
        {
        }
        // Route
        $this->view_data['analytics_route'] = route('hodor.analytics.index');
        // Title
        $this->view_data['page_title']      = 'Analytics' . (!empty($analytics_year) ? ' - ' . $analytics_year : '');
        $this->view_data['analytics_year']  = $analytics_year;
        $this->view_data['analytics_years'] = array_combine($year_range, $year_range);
        $this->view_data['analytics_data']  = getAnalyticsData($analytics_year);
        // Add missing months
        $months = [
            "January"   => 0,
            "February"  => 0,
            "March"     => 0,
            "April"     => 0,
            "May"       => 0,
            "June"      => 0,
            "July"      => 0,
            "August"    => 0,
            "September" => 0,
            "October"   => 0,
            "November"  => 0,
            "December"  => 0,
        ];
        $this->view_data['analytics_data']['graphs']['created_at'] = array_merge($months, $this->view_data['analytics_data']['graphs']['created_at']);
        $this->view_data['analytics_data']['graphs']['pickup_date'] = array_merge($months, $this->view_data['analytics_data']['graphs']['pickup_date']);
        return view('hodor::analytics.index', $this->view_data);
    }

    /**
     * Page that presents analytics data on budget
     *
     * @return Renderable
     */
    public function budget(BudgetAnalyticsSearchRequest $request)
    {
        // Get the analytics range data
        $start_year = Reservation::selectRaw('date_format(min(pickup_date), "%Y") as date')->first()->date;
        $year_range = range(date('Y'), $start_year);
        $analytics_year     = null;
        $analytics_month    = null;
        try {
            $analytics_year     = Carbon::createFromFormat('Y', $request->analytics_year)->format('Y');
            $analytics_year     = in_array($analytics_year, $year_range) ? $analytics_year : null;
            $analytics_month    = Carbon::createFromFormat('m', $request->analytics_month)->format('m');
        } catch (\Exception $e)
        {
        }
        // Route
        $this->view_data['analytics_route'] = route('hodor.analytics.budget');
        // Title
        $this->view_data['page_title']      = 'Budget analytics' . (!empty($analytics_month) ? ' - ' . Carbon::createFromFormat('m', $analytics_month)->format('F') : '') . (!empty($analytics_year) ? ' - ' . $analytics_year : '');
        $this->view_data['analytics_year']  = $analytics_year;
        $this->view_data['analytics_month'] = (int) $analytics_month;
        $this->view_data['analytics_years'] = array_combine($year_range, $year_range);
        $this->view_data['analytics_data']  = getBudgetAnalyticsData($analytics_year, $analytics_month);
        $this->view_data['analytics_months'] = [
            "January"   => 1,
            "February"  => 2,
            "March"     => 3,
            "April"     => 4,
            "May"       => 5,
            "June"      => 6,
            "July"      => 7,
            "August"    => 8,
            "September" => 9,
            "October"   => 10,
            "November"  => 11,
            "December"  => 12,
        ];
        if(empty($analytics_month))
        {
            $this->view_data['analytics_dates'] = [
                "January"   => 0,
                "February"  => 0,
                "March"     => 0,
                "April"     => 0,
                "May"       => 0,
                "June"      => 0,
                "July"      => 0,
                "August"    => 0,
                "September" => 0,
                "October"   => 0,
                "November"  => 0,
                "December"  => 0,
            ];
        }
        else
        {
            $date = Carbon::createFromFormat('Y-m', $analytics_year . '-' . $analytics_month);
            $this->view_data['analytics_dates'] = array_fill_keys(range(1, $date->lastOfMonth()->format('d')), 0);
        }
        $this->view_data['analytics_data']['graphs']['analytics_dates'] = $this->view_data['analytics_dates'];
        $this->view_data['analytics_data']['graphs']['sum_pickup_date'] = array_replace($this->view_data['analytics_dates'], $this->view_data['analytics_data']['graphs']['sum_pickup_date']);
        $this->view_data['analytics_data']['graphs']['pickup_date']     = array_replace($this->view_data['analytics_dates'], $this->view_data['analytics_data']['graphs']['pickup_date']);

        return view('hodor::analytics.budget', $this->view_data);
    }

    /**
     * Page that presents budget comparisons data based on selected date ranges
     *
     * @return Renderable
     */
    public function budgetComparisons(BudgetComparisonsSearchRequest $request)
    {
        // Default dates if not provided
        $currentYear    = Carbon::now()->year;
        $lastYear       = $currentYear - 1;
        $today          = Carbon::now()->format('Y-m-d');
        $sameLastYear   = Carbon::now()->subYear()->format('Y-m-d');

        // Initialize with January 1st of current year to today, and corresponding dates last year
        $defaultDateFrom        = Carbon::createFromDate($currentYear, 1, 1)->format('Y-m-d');
        $defaultDateTo          = $today;
        $defaultComparisonFrom  = Carbon::createFromDate($lastYear, 1, 1)->format('Y-m-d');
        $defaultComparisonTo    = $sameLastYear;

        // Get date range parameters - use exact dates from request or defaults
        $date_from          = $request->date_from ?? $defaultDateFrom;
        $date_to            = $request->date_to ?? $defaultDateTo;
        $comparison_from    = $request->comparison_from ?? $defaultComparisonFrom;
        $comparison_to      = $request->comparison_to ?? $defaultComparisonTo;

        // Pass date parameters to view
        $this->view_data['date_from']       = $date_from;
        $this->view_data['date_to']         = $date_to;
        $this->view_data['comparison_from'] = $comparison_from;
        $this->view_data['comparison_to']   = $comparison_to;

        // Get data
        $this->view_data['analytics_data'] = $this->getBudgetComparisonsData(
            $date_from,
            $date_to,
            $comparison_from,
            $comparison_to
        );

        // Title of page
        $this->view_data['page_title'] = 'Budget comparisons - ' . $date_from . ' to ' . $date_to;

        return view('hodor::analytics.budget_comparisons', $this->view_data);
    }

    /**
     * Handle budget comparisons form submission and redirect to budgetComparisons with GET parameters
     * Used to make sure we have the date range fields required
     *
     * @param BudgetComparisonsSearchRequest $request
     * @return RedirectResponse
     */
    public function submitBudgetComparisons(BudgetComparisonsPostRequest $request)
    {
        return redirect()->route('hodor.analytics.budget.comparisons', [
            'date_from' => $request->date_from,
            'date_to' => $request->date_to,
            'comparison_from' => $request->comparison_from,
            'comparison_to' => $request->comparison_to
        ]);
    }

    /**
     * Page that presents budget per day metrics
     *
     * @param BudgetPerDaySearchRequest $request
     * @return Renderable
     */
    public function budgetPerDay(BudgetPerDaySearchRequest $request)
    {
        // Get the analytics range data
        $start_year = Reservation::selectRaw('date_format(min(pickup_date), "%Y") as date')->first()->date;
        $year_range = range(date('Y'), $start_year);
        // initialize analytics_year
        $analytics_year = date('Y');

        try {
            $analytics_year = Carbon::createFromFormat('Y', $request->analytics_year)->format('Y');
            $analytics_year = in_array($analytics_year, $year_range) ? $analytics_year : date('Y');
        } catch (\Exception $e) {
        }

        // Get the selected group (if any)
        $selected_group = $request->group;
        $this->view_data['analytics_year'] = $analytics_year;
        // create an associative array with year as key and value
        $this->view_data['analytics_years'] = array_combine($year_range, $year_range);
        $this->view_data['selected_group'] = $selected_group;

        // Get the budget per day data
        $this->view_data['analytics_data'] = $this->getBudgetPerDayData($analytics_year, $selected_group);

        // Get all available groups for the dropdown
        $this->view_data['available_groups'] = $this->getAvailableGroups($analytics_year);

        // Define months for display
        $this->view_data['analytics_months'] = [
            "January"   => 1,
            "February"  => 2,
            "March"     => 3,
            "April"     => 4,
            "May"       => 5,
            "June"      => 6,
            "July"      => 7,
            "August"    => 8,
            "September" => 9,
            "October"   => 10,
            "November"  => 11,
            "December"  => 12,
        ];

        // Title
        $title = 'Budget Per Day Analytics - ' . $analytics_year;
        if ($selected_group) {
            $title .= ' (Group ' . $selected_group . ')';
        }
        $this->view_data['page_title'] = $title;

        return view('hodor::analytics.budget_per_day', $this->view_data);
    }


    /**
     * Private method to calculate all date fet budget comparisons page
     *
     * @param string $date_from
     * @param string $date_to
     * @param string $comparison_from
     * @param string $comparison_to
     * @return array
     */
    private function getBudgetComparisonsData(
        string $date_from,
        string $date_to,
        string $comparison_from,
        string $comparison_to
    )
    {
        // Init base queries
        $reservations_reference     = Reservation::selectRaw('count(*) as reservations, sum(final_price) as revenue, sum(total_days) as total_days');
        $reservations_comparison    = Reservation::selectRaw('count(*) as reservations, sum(final_price) as revenue, sum(total_days) as total_days');

        // Core query conditions, we do not want to count no-show reservations
        $reservations_reference->where('show', 'show');
        $reservations_comparison->where('show', 'show');

        // Apply date range filters with proper date formatting so as to include all reservations regardless of time
        $reservations_reference->whereBetween(DB::raw('date(created_at)'), [
            $date_from,
            $date_to
        ]);
        $reservations_comparison->whereBetween(DB::raw('date(created_at)'), [
            $comparison_from,
            $comparison_to
        ]);

        // Count data
        $reservationsReferenceData  = $reservations_reference->first();
        $reservationsComparisonData = $reservations_comparison->first();

        // Reference period totals
        $data['counts']['total_reservations_reference']   = $reservationsReferenceData->reservations;
        $data['counts']['total_revenue_reference']        = $reservationsReferenceData->revenue;
        $data['counts']['total_days_reference']           = $reservationsReferenceData->total_days;
        // Calculate average revenue per day for reference period
        $data['counts']['avg_revenue_per_day_reference']  = $reservationsReferenceData->total_days > 0 ?
            $reservationsReferenceData->revenue / $reservationsReferenceData->total_days : 0;

        // Comparison period totals
        $data['counts']['total_reservations_comparison']  = $reservationsComparisonData->reservations;
        $data['counts']['total_revenue_comparison']       = $reservationsComparisonData->revenue;
        $data['counts']['total_days_comparison']          = $reservationsComparisonData->total_days;
        // Calculate average revenue per day for comparison period
        $data['counts']['avg_revenue_per_day_comparison'] = $reservationsComparisonData->total_days > 0 ?
            $reservationsComparisonData->revenue / $reservationsComparisonData->total_days : 0;

        // Calculate differences and percentages for totals
        $data['counts']['diff_reservations'] = $data['counts']['total_reservations_reference'] - $data['counts']['total_reservations_comparison'];
        $data['counts']['pct_reservations'] = $data['counts']['total_reservations_comparison'] > 0 ?
            ($data['counts']['diff_reservations'] / $data['counts']['total_reservations_comparison']) * 100 : 0;

        $data['counts']['diff_revenue'] = $data['counts']['total_revenue_reference'] - $data['counts']['total_revenue_comparison'];
        $data['counts']['pct_revenue'] = $data['counts']['total_revenue_comparison'] > 0 ?
            ($data['counts']['diff_revenue'] / $data['counts']['total_revenue_comparison']) * 100 : 0;

        $data['counts']['diff_days'] = $data['counts']['total_days_reference'] - $data['counts']['total_days_comparison'];
        $data['counts']['pct_days'] = $data['counts']['total_days_comparison'] > 0 ?
            ($data['counts']['diff_days'] / $data['counts']['total_days_comparison']) * 100 : 0;

        // Calculate difference and percentage for average revenue per day
        $data['counts']['diff_avg_revenue_per_day'] = $data['counts']['avg_revenue_per_day_reference'] - $data['counts']['avg_revenue_per_day_comparison'];
        $data['counts']['pct_avg_revenue_per_day'] = $data['counts']['avg_revenue_per_day_comparison'] > 0 ?
            ($data['counts']['diff_avg_revenue_per_day'] / $data['counts']['avg_revenue_per_day_comparison']) * 100 : 0;

        // Get data segmented by car groups for reference period
        $groups_reference = Reservation::selectRaw('listing_group, count(*) as reservations, sum(final_price) as revenue, sum(total_days) as total_days')
            ->whereBetween(DB::raw('date(created_at)'), [$date_from, $date_to])
            ->groupBy('listing_group')
            ->get();

        // Get data segmented by car groups for comparison period
        $groups_comparison = Reservation::selectRaw('listing_group, count(*) as reservations, sum(final_price) as revenue, sum(total_days) as total_days')
            ->whereBetween(DB::raw('date(created_at)'), [$comparison_from, $comparison_to])
            ->groupBy('listing_group')
            ->get();

        // Format group data for reference period
        $data['groups']['reference'] = [];
        foreach ($groups_reference as $group) {
            $data['groups']['reference'][$group->listing_group] = [
                'reservations' => $group->reservations,
                'revenue' => $group->revenue,
                'total_days' => $group->total_days,
                'avg_revenue_per_day' => $group->total_days > 0 ? $group->revenue / $group->total_days : 0
            ];
        }

        // Format group data for comparison period
        $data['groups']['comparison'] = [];
        foreach ($groups_comparison as $group) {
            $data['groups']['comparison'][$group->listing_group] = [
                'reservations' => $group->reservations,
                'revenue' => $group->revenue,
                'total_days' => $group->total_days,
                'avg_revenue_per_day' => $group->total_days > 0 ? $group->revenue / $group->total_days : 0
            ];
        }

        // Combine all groups and calculate differences
        $allGroups = array_unique(array_merge(
            array_keys($data['groups']['reference']),
            array_keys($data['groups']['comparison'])
        ));
        sort($allGroups);

        $data['groups']['all'] = [];
        foreach ($allGroups as $group) {
            // Get reference data
            $refReservations    = isset($data['groups']['reference'][$group]) ? $data['groups']['reference'][$group]['reservations'] : 0;
            $refRevenue         = isset($data['groups']['reference'][$group]) ? $data['groups']['reference'][$group]['revenue'] : 0;
            $refDays            = isset($data['groups']['reference'][$group]) ? $data['groups']['reference'][$group]['total_days'] : 0;
            $refAvgRevenue      = isset($data['groups']['reference'][$group]) ? $data['groups']['reference'][$group]['avg_revenue_per_day'] : 0;

            // Get comparison data
            $compReservations   = isset($data['groups']['comparison'][$group]) ? $data['groups']['comparison'][$group]['reservations'] : 0;
            $compRevenue        = isset($data['groups']['comparison'][$group]) ? $data['groups']['comparison'][$group]['revenue'] : 0;
            $compDays           = isset($data['groups']['comparison'][$group]) ? $data['groups']['comparison'][$group]['total_days'] : 0;
            $compAvgRevenue     = isset($data['groups']['comparison'][$group]) ? $data['groups']['comparison'][$group]['avg_revenue_per_day'] : 0;

            // Calculate differences
            $diffReservations   = $refReservations - $compReservations;
            $diffRevenue        = $refRevenue - $compRevenue;
            $diffDays           = $refDays - $compDays;
            $diffAvgRevenue     = $refAvgRevenue - $compAvgRevenue;

            // Calculate percentages
            $pctReservations    = $compReservations > 0 ? ($diffReservations / $compReservations) * 100 : 0;
            $pctRevenue         = $compRevenue > 0 ? ($diffRevenue / $compRevenue) * 100 : 0;
            $pctDays            = $compDays > 0 ? ($diffDays / $compDays) * 100 : 0;
            $pctAvgRevenue      = $compAvgRevenue > 0 ? ($diffAvgRevenue / $compAvgRevenue) * 100 : 0;

            $data['groups']['all'][$group] = [
                'reference' => [
                    'reservations' => $refReservations,
                    'revenue' => $refRevenue,
                    'total_days' => $refDays,
                    'avg_revenue_per_day' => $refAvgRevenue
                ],
                'comparison' => [
                    'reservations' => $compReservations,
                    'revenue' => $compRevenue,
                    'total_days' => $compDays,
                    'avg_revenue_per_day' => $compAvgRevenue
                ],
                'differences' => [
                    'reservations' => $diffReservations,
                    'revenue' => $diffRevenue,
                    'total_days' => $diffDays,
                    'avg_revenue_per_day' => $diffAvgRevenue
                ],
                'percentages' => [
                    'reservations' => $pctReservations,
                    'revenue' => $pctRevenue,
                    'total_days' => $pctDays,
                    'avg_revenue_per_day' => $pctAvgRevenue
                ]
            ];
        }

        // Table data
//        $data['tables']['listing_model_sum']     = Reservation::getReservationShowDataAggregatedBy('listing_model', $analytics_year, $analytics_month, ['sum' => 'final_price'])->toArray();
//        $data['tables']['listing_group_sum']     = Reservation::getReservationShowDataAggregatedBy('listing_group', $analytics_year, $analytics_month, ['sum' => 'final_price'])->toArray();
//
//        // Graph data
//        $data['graphs']['sum_pickup_date']       = Reservation::getReservationShowDataMonthlyAggregatedBy('pickup_date', $analytics_year, $analytics_month, ['sum' => 'final_price'])->toArray();
//        $data['graphs']['pickup_date']           = Reservation::getReservationShowDataMonthlyAggregatedBy('pickup_date', $analytics_year, $analytics_month)->toArray();
//        $data['graphs']['sum_created_date']      = Reservation::getReservationShowDataMonthlyAggregatedBy('created_at', $analytics_year, $analytics_month, ['sum' => 'final_price'])->toArray();
//        $data['graphs']['created_date']          = Reservation::getReservationShowDataMonthlyAggregatedBy('created_at', $analytics_year, $analytics_month)->toArray();

        return $data;
    }

    /**
     * Get all available listing groups for the given year
     *
     * @param string $year
     * @return array
     */
    private function getAvailableGroups(string $year)
    {
        $groups = Reservation::select('listing_group')
            ->where('show', 'show')
            ->whereRaw('YEAR(pickup_date) = ?', [$year])
            ->groupBy('listing_group')
            ->orderBy('listing_group')
            ->pluck('listing_group')
            ->toArray();

        return $groups;
    }

    /**
     * Private method to calculate budget per day metrics
     *
     * @param string $year
     * @param string|null $group Filter by specific group
     * @return array
     */
    private function getBudgetPerDayData(string $year, ?string $group = null)
    {
        $data = [];

        // Initialize months array for consistent data structure
        $months = [
            '01' => 'January',
            '02' => 'February',
            '03' => 'March',
            '04' => 'April',
            '05' => 'May',
            '06' => 'June',
            '07' => 'July',
            '08' => 'August',
            '09' => 'September',
            '10' => 'October',
            '11' => 'November',
            '12' => 'December'
        ];

        // Get monthly totals for the year
        $query = Reservation::selectRaw('MONTH(pickup_date) as month, COUNT(*) as reservations, SUM(final_price) as revenue, SUM(total_days) as total_days')
            ->onlyShow()
            ->whereRaw('YEAR(pickup_date) = ?', [$year]);

        // Filter by group if specified
        if ($group) {
            $query->where('listing_group', $group);
        }

        $monthlyTotals = $query->groupBy(DB::raw('MONTH(pickup_date)'))
            ->orderBy(DB::raw('MONTH(pickup_date)'))
            ->get()
            ->keyBy('month');

        // Initialize monthly data structure
        foreach ($months as $monthNum => $monthName) {
            $monthInt = (int)$monthNum;

            // Default values if no data exists for this month
            $reservations = 0;
            $revenue = 0;
            $totalDays = 0;
            $avgPricePerDay = 0;

            // If we have data for this month, use it
            if ($monthlyTotals->has($monthInt)) {
                $reservations = $monthlyTotals[$monthInt]->reservations;
                $revenue = $monthlyTotals[$monthInt]->revenue;
                $totalDays = $monthlyTotals[$monthInt]->total_days;

                // Calculate average price per day
                $avgPricePerDay = $totalDays > 0 ? $revenue / $totalDays : 0;
            }

            // Store monthly totals
            $data['monthly'][$monthName] = [
                'reservations' => $reservations,
                'revenue' => $revenue,
                'total_days' => $totalDays,
                'avg_price_per_day' => $avgPricePerDay
            ];
        }

        // Calculate yearly totals
        $yearlyReservations = $monthlyTotals->sum('reservations');
        $yearlyRevenue = $monthlyTotals->sum('revenue');
        $yearlyTotalDays = $monthlyTotals->sum('total_days');
        $yearlyAvgPricePerDay = $yearlyTotalDays > 0 ? $yearlyRevenue / $yearlyTotalDays : 0;

        // Store yearly totals
        $data['yearly'] = [
            'reservations' => $yearlyReservations,
            'revenue' => $yearlyRevenue,
            'total_days' => $yearlyTotalDays,
            'avg_price_per_day' => $yearlyAvgPricePerDay
        ];

        return $data;
    }

    /**
     * Display seasonal performance analytics
     *
     * @param AnalyticsSearchRequest $request
     * @return Renderable
     */
    public function seasonalPerformance(AnalyticsSearchRequest $request)
    {
        // Get year range data
        $start_year = Reservation::selectRaw('date_format(min(pickup_date), "%Y") as date')->first()->date;
        $year_range = range(date('Y'), $start_year);
        $analytics_year = null;

        try {
            $analytics_year = Carbon::createFromFormat('Y', $request->analytics_year)->format('Y');
            $analytics_year = in_array($analytics_year, $year_range) ? $analytics_year : date('Y');
        } catch (\Exception $e) {
            $analytics_year = date('Y');
        }

        // Route and title
        $this->view_data['analytics_route'] = route('hodor.analytics.seasonal');
        $this->view_data['page_title'] = 'Seasonal Performance - ' . $analytics_year;
        $this->view_data['analytics_year'] = $analytics_year;
        $this->view_data['analytics_years'] = array_combine($year_range, $year_range);

        // Get seasonal data
        $this->view_data['analytics_data'] = $this->getSeasonalPerformanceData($analytics_year);

        return view('hodor::analytics.seasonal_performance', $this->view_data);
    }

    /**
     * Get seasonal performance data
     *
     * @param string $year
     * @return array
     */
    private function getSeasonalPerformanceData(string $year)
    {
        $data = [];

        // Define seasons
        $seasons = [
            'Winter' => ['12', '01', '02'],
            'Spring' => ['03', '04', '05'],
            'Summer' => ['06', '07', '08'],
            'Fall' => ['09', '10', '11']
        ];

        // Get data for each season
        foreach ($seasons as $season => $months) {
            $query = Reservation::selectRaw('COUNT(*) as reservations, SUM(final_price) as revenue, SUM(total_days) as total_days')
                ->where('show', 'show');

            $monthConditions = [];
            foreach ($months as $month) {
                // Handle December of previous year for Winter
                $queryYear = $year;
                if ($month == '12' && $season == 'Winter') {
                    $queryYear = (int)$year - 1;
                }

                $monthConditions[] = "DATE_FORMAT(pickup_date, '%Y-%m') = '$queryYear-$month'";
            }

            $query->whereRaw('(' . implode(' OR ', $monthConditions) . ')');
            $seasonData = $query->first();

            $data['seasons'][$season] = [
                'reservations' => $seasonData->reservations,
                'revenue' => $seasonData->revenue,
                'total_days' => $seasonData->total_days,
                'avg_revenue_per_day' => $seasonData->total_days > 0 ? $seasonData->revenue / $seasonData->total_days : 0
            ];
        }

        // Get top performing car groups by season
        foreach ($seasons as $season => $months) {
            $query = Reservation::selectRaw('listing_group, COUNT(*) as reservations, SUM(final_price) as revenue')
                ->where('show', 'show');

            $monthConditions = [];
            foreach ($months as $month) {
                $queryYear = $year;
                if ($month == '12' && $season == 'Winter') {
                    $queryYear = (int)$year - 1;
                }

                $monthConditions[] = "DATE_FORMAT(pickup_date, '%Y-%m') = '$queryYear-$month'";
            }

            $query->whereRaw('(' . implode(' OR ', $monthConditions) . ')');
            $data['top_groups'][$season] = $query->groupBy('listing_group')
                ->orderBy('revenue', 'desc')
                ->limit(5)
                ->get()
                ->toArray();
        }

        return $data;
    }

    /**
     * Display customer retention analytics
     *
     * @param AnalyticsSearchRequest $request
     * @return Renderable
     */
    public function customerRetention(AnalyticsSearchRequest $request)
    {
        // Get year range data
        $start_year = Reservation::selectRaw('date_format(min(pickup_date), "%Y") as date')->first()->date;
        $year_range = range(date('Y'), $start_year);
        $analytics_year = null;

        try {
            $analytics_year = Carbon::createFromFormat('Y', $request->analytics_year)->format('Y');
            $analytics_year = in_array($analytics_year, $year_range) ? $analytics_year : date('Y');
        } catch (\Exception $e) {
            $analytics_year = date('Y');
        }

        // Route and title
        $this->view_data['analytics_route'] = route('hodor.analytics.retention');
        $this->view_data['page_title'] = 'Customer Retention - ' . $analytics_year;
        $this->view_data['analytics_year'] = $analytics_year;
        $this->view_data['analytics_years'] = array_combine($year_range, $year_range);

        // Get retention data
        $this->view_data['analytics_data'] = $this->getCustomerRetentionData($analytics_year);

        return view('hodor::analytics.customer_retention', $this->view_data);
    }

    /**
     * Get customer retention data
     *
     * @param string $year
     * @return array
     */
    private function getCustomerRetentionData(string $year)
    {
        $data = [];

        // Get new vs returning customers
        $allCustomers = Reservation::select('customer_id')
            ->where('show', 'show')
            ->whereRaw("YEAR(pickup_date) = ?", [$year])
            ->groupBy('customer_id')
            ->get()
            ->pluck('customer_id')
            ->toArray();

        $previousCustomers = Reservation::select('customer_id')
            ->where('show', 'show')
            ->whereRaw("YEAR(pickup_date) < ?", [$year])
            ->groupBy('customer_id')
            ->get()
            ->pluck('customer_id')
            ->toArray();

        $returningCustomers = array_intersect($allCustomers, $previousCustomers);
        $newCustomers = array_diff($allCustomers, $previousCustomers);

        $data['customer_counts'] = [
            'total' => count($allCustomers),
            'new' => count($newCustomers),
            'returning' => count($returningCustomers),
            'retention_rate' => count($previousCustomers) > 0 ?
                (count($returningCustomers) / count($previousCustomers)) * 100 : 0
        ];

        // Get revenue from new vs returning customers
        $returningRevenue = Reservation::whereIn('customer_id', $returningCustomers)
            ->where('show', 'show')
            ->whereRaw("YEAR(pickup_date) = ?", [$year])
            ->sum('final_price');

        $newRevenue = Reservation::whereIn('customer_id', $newCustomers)
            ->where('show', 'show')
            ->whereRaw("YEAR(pickup_date) = ?", [$year])
            ->sum('final_price');

        $data['revenue'] = [
            'total' => $returningRevenue + $newRevenue,
            'new' => $newRevenue,
            'returning' => $returningRevenue
        ];

        // Get monthly retention rates
        for ($month = 1; $month <= 12; $month++) {
            $monthStr = str_pad($month, 2, '0', STR_PAD_LEFT);

            $monthCustomers = Reservation::select('customer_id')
                ->where('show', 'show')
                ->whereRaw("DATE_FORMAT(pickup_date, '%Y-%m') = ?", ["$year-$monthStr"])
                ->groupBy('customer_id')
                ->get()
                ->pluck('customer_id')
                ->toArray();

            $monthReturningCustomers = array_intersect($monthCustomers, $previousCustomers);

            $data['monthly'][$monthStr] = [
                'total' => count($monthCustomers),
                'new' => count(array_diff($monthCustomers, $previousCustomers)),
                'returning' => count($monthReturningCustomers),
                'retention_rate' => count($previousCustomers) > 0 ?
                    (count($monthReturningCustomers) / count($previousCustomers)) * 100 : 0
            ];
        }

        return $data;
    }

    /**
     * Display fleet utilization analytics
     *
     * @param AnalyticsSearchRequest $request
     * @return Renderable
     */
    public function fleetUtilization(AnalyticsSearchRequest $request)
    {
        // Get year range data
        $start_year = Reservation::selectRaw('date_format(min(pickup_date), "%Y") as date')->first()->date;
        $year_range = range(date('Y'), $start_year);
        $analytics_year = null;
        $analytics_month = null;

        try {
            $analytics_year = Carbon::createFromFormat('Y', $request->analytics_year)->format('Y');
            $analytics_year = in_array($analytics_year, $year_range) ? $analytics_year : date('Y');

            if ($request->analytics_month) {
                $analytics_month = Carbon::createFromFormat('m', $request->analytics_month)->format('m');
            }
        } catch (\Exception $e) {
            $analytics_year = date('Y');
        }

        // Route and title
        $this->view_data['analytics_route'] = route('hodor.analytics.fleet');
        $this->view_data['page_title'] = 'Fleet Utilization' .
            (!empty($analytics_month) ? ' - ' . Carbon::createFromFormat('m', $analytics_month)->format('F') : '') .
            (!empty($analytics_year) ? ' - ' . $analytics_year : '');

        $this->view_data['analytics_year'] = $analytics_year;
        $this->view_data['analytics_month'] = (int) $analytics_month;
        $this->view_data['analytics_years'] = array_combine($year_range, $year_range);
        $this->view_data['analytics_months'] = [
            "January" => 1, "February" => 2, "March" => 3, "April" => 4,
            "May" => 5, "June" => 6, "July" => 7, "August" => 8,
            "September" => 9, "October" => 10, "November" => 11, "December" => 12,
        ];

        // Get utilization data
        $this->view_data['analytics_data'] = $this->getFleetUtilizationData($analytics_year, $analytics_month);

        return view('hodor::analytics.fleet_utilization', $this->view_data);
    }

    /**
     * Get fleet utilization data
     *
     * @param string $year
     * @param string|null $month
     * @return array
     */
    private function getFleetUtilizationData(string $year, ?string $month = null)
    {
        $data = [];

        // Calculate date range
        $startDate = Carbon::createFromDate($year, $month ?: 1, 1)->startOfDay();
        $endDate = $month ?
            Carbon::createFromDate($year, $month, 1)->endOfMonth()->endOfDay() :
            Carbon::createFromDate($year, 12, 31)->endOfDay();

        // Get total days in period
        $totalDays = $endDate->diffInDays($startDate) + 1;

        // Get total rental days by car group
        $rentalDaysByGroup = Reservation::selectRaw('listing_group, SUM(total_days) as total_rental_days')
            ->where('show', 'show')
            ->where(function($query) use ($startDate, $endDate) {
                // Reservations that overlap with our period
                $query->where(function($q) use ($startDate, $endDate) {
                    $q->where('pickup_date', '<=', $endDate)
                      ->where('dropoff_date', '>=', $startDate);
                });
            })
            ->groupBy('listing_group')
            ->get()
            ->keyBy('listing_group')
            ->toArray();

        // Get car count by group
        // Note: This is a simplified approach. In a real system, you'd need to account for
        // cars being added/removed from the fleet during the period
        $carCountByGroup = DB::table('listings')
            ->selectRaw('listing_group, COUNT(*) as car_count')
            ->groupBy('listing_group')
            ->get()
            ->keyBy('listing_group')
            ->toArray();

        // Calculate utilization rates
        foreach ($carCountByGroup as $group => $data) {
            $carCount = $data->car_count;
            $maxPossibleDays = $carCount * $totalDays;
            $actualRentalDays = isset($rentalDaysByGroup[$group]) ?
                $rentalDaysByGroup[$group]['total_rental_days'] : 0;

            $utilizationRate = $maxPossibleDays > 0 ?
                ($actualRentalDays / $maxPossibleDays) * 100 : 0;

            $data['utilization'][$group] = [
                'car_count' => $carCount,
                'max_possible_days' => $maxPossibleDays,
                'actual_rental_days' => $actualRentalDays,
                'utilization_rate' => $utilizationRate
            ];
        }

        // Get monthly utilization if year view
        if (!$month) {
            for ($m = 1; $m <= 12; $m++) {
                $monthStr = str_pad($m, 2, '0', STR_PAD_LEFT);
                $monthStart = Carbon::createFromDate($year, $m, 1)->startOfDay();
                $monthEnd = Carbon::createFromDate($year, $m, 1)->endOfMonth()->endOfDay();
                $monthDays = $monthEnd->diffInDays($monthStart) + 1;

                $monthRentalDays = Reservation::where('show', 'show')
                    ->where(function($query) use ($monthStart, $monthEnd) {
                        $query->where(function($q) use ($monthStart, $monthEnd) {
                            $q->where('pickup_date', '<=', $monthEnd)
                              ->where('dropoff_date', '>=', $monthStart);
                        });
                    })
                    ->sum('total_days');

                $totalCars = array_sum(array_column($carCountByGroup, 'car_count'));
                $maxPossibleDays = $totalCars * $monthDays;

                $data['monthly'][$monthStr] = [
                    'total_cars' => $totalCars,
                    'max_possible_days' => $maxPossibleDays,
                    'actual_rental_days' => $monthRentalDays,
                    'utilization_rate' => $maxPossibleDays > 0 ?
                        ($monthRentalDays / $maxPossibleDays) * 100 : 0
                ];
            }
        }

        return $data;
    }
}
