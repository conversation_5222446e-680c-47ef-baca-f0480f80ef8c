<?php

namespace App\Http\Controllers;

use Artesaos\SEOTools\Facades\OpenGraph;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;

use App\Group;
use App\Language;
use App\Location;
use App\Popup;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Lang;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Session;
use App\Helpers\TimeRange;
use Illuminate\Support\Arr;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    protected $group_data;
    protected $view_data = [];
    protected $default_timepicker_value = '12:00';

    public function __construct()
    {
        if(strpos(url(''), 'index.php') !== false && isset($_SERVER) && isset($_SERVER['PATH_INFO'])){
            header('Location: '.str_replace('/index.php', '', url('')) . $_SERVER['PATH_INFO']);
            exit;
        }
        // Switch between session and cookie

        // flush the session variables
        foreach (['pickup_location', 'pickup_date', 'pickup_time', 'dropoff_location', 'dropoff_date', 'dropoff_time'] as $session_value){
            if(Session::get($session_value)){
                setcookie($session_value, Session::get($session_value), 0, '/');
                Session::forget($session_value);
            }
        }

        // initialize view data
        // fer front end
        $this->view_data['car_list_selected'] = '';
        $this->view_data['offers_selected'] = '';
        $this->view_data['about_selected'] = '';
        $this->view_data['contact_selected'] = '';
        $this->view_data['post_list_selected'] = '';
        $this->view_data['faq_selected'] = '';
        $this->view_data['policy_selected'] = '';
        $this->view_data['localised_selected'] = '';

        $this->view_data['pickup_date']  = Arr::get($_COOKIE, 'pickup_date',  Carbon::now()->addDays(2)->format('d-m-Y'));
        $this->view_data['dropoff_date'] = Arr::get($_COOKIE, 'dropoff_date', Carbon::now()->addDays(9)->format('d-m-Y'));

        $this->view_data['pickup_time']  = Arr::get($_COOKIE, 'pickup_time',  $this->default_timepicker_value);
        $this->view_data['dropoff_time'] = Arr::get($_COOKIE, 'dropoff_time', $this->default_timepicker_value);

        $this->view_data['url_ref'] = 'home';

        // fer admin end
        $this->view_data['reservations_group_selected'] = '';
        $this->view_data['content_group_selected'] = '';
        $this->view_data['dashboard_selected'] = '';
        $this->view_data['add_listing_selected'] = '';
        $this->view_data['listings_selected'] = '';
        $this->view_data['categories_selected'] = '';
        $this->view_data['add_location_selected'] = '';
        $this->view_data['locations_selected'] = '';
        $this->view_data['add_accessory_selected'] = '';
        $this->view_data['accessories_selected'] = '';
        $this->view_data['add_coupon_selected'] = '';
        $this->view_data['coupons_selected'] = '';
        $this->view_data['popup_selected'] = '';
        $this->view_data['add_group_selected'] = '';
        $this->view_data['groups_selected'] = '';
        $this->view_data['quotes_selected'] = '';
        $this->view_data['feedback_selected'] = '';
        $this->view_data['reservations_selected'] = '';
        $this->view_data['offers_selected'] = '';
        $this->view_data['repeatingClients_selected'] = '';
        $this->view_data['customers_selected'] = '';
        $this->view_data['newsletters_selected'] = '';
        $this->view_data['pricing_selected'] = '';
        $this->view_data['blog_selected'] = '';

        $locations = Location::all()->pluck('name', 'id')->all();
        $this->view_data['locations_dropdown_pickup'] = $locations;
        $this->view_data['locations_dropdown_dropoff'] = ['' => Lang::get('common.same_as_pickup')] + $locations;
        // Load groups
        $this->group_data = Group::orderByRaw('group_order ASC, id ASC')->get();
        $this->view_data['groups_menu_seo'] = $this->group_data->pluck('fullNameSEO', 'id');
        //the default time for timepickers (used by js clear form action)
        $this->view_data['default_time'] = $this->default_timepicker_value;
        // get the timerange values for the timepicker dropdowns
        $this->view_data['time_ranges']  = TimeRange::create_time_range('00:30', '23:30', 30, array('23:50'));

        // Get the languages enabled in the DB for other language link generation in header footer
        // Languages not enabled in the DB can still be accessed and have the programmatical language atributes set
        $this->view_data['enabledLanguages'] = Language::where('enabled', '=', true)->get();

        // Set a config item to access available translation locales through validation and everything else needed
        Config::set('translationLocales', Language::all()->pluck('locale', 'id'));
        // initialize url_variables to empty
        $this->view_data['url_variables'] = '';
        $this->view_data['current_locale'] = \LaravelLocalization::getCurrentLocale();

        // Get the offer data
        $this->view_data['popup'] = false;
        $this->popup();

        // Opengraph data initialization
        // the title, description, image and type are overwritten, where needed, in the relevant controller method
        // by calling the overwriteOpengraph() method
        OpenGraph::setTitle(trans('common.site_name'));
        OpenGraph::setDescription(trans('common.default_meta_description'));
        OpenGraph::setUrl(Request::url());
        OpenGraph::setType('website');
    }

    /**
     * Setup the layout used by the controller.
     *
     * @return void
     */
    protected function setupLayout()
    {

        if ( ! is_null($this->layout))
        {
            $this->layout = View::make($this->layout, $this->view_data);
        }
    }

    /**
     * Overwrite the opengraph data, should be called in the end of the controller relevant method (index, show, etc.)
     *
     * @param $title
     * @param $description
     * @param $image
     * @return void
     */
    protected function overwriteOpengraph($title, $description, $image = null, $type = 'website')
    {
        OpenGraph::setTitle($title);
        OpenGraph::setDescription($description);
        OpenGraph::setType($type);
        if($image)
        {
            OpenGraph::addImage($image);
        }
        else
        {
            // add default image
            OpenGraph::addImage(asset('images/eurodollar-social-share.jpg'));
        }
    }

    private function popup()
    {
        // If admin page, return
        if(request()->route() && request()->route()->getPrefix() === "/admin")
        {
            return $this;
        }

        // Get the popup
        $popup = Popup::first();
        // If there is no popup or the popup is not active, return
        if(
            !$popup ||
            !$popup->active
        )
        {
            return $this;
        }
        // Check dates
        $now = Carbon::now();
        $start_day = (new Carbon($popup->start_date))->timestamp;
        $end_date = (new Carbon($popup->end_date . " 23:59"))->timestamp;;
        $popupCookie = Arr::get($_COOKIE, 'popup_offer');
        // If cookie is older than date updated or current date not between start and end date, return
        if(
            (int)$popupCookie > $popup->updated_at->timestamp ||
            $start_day > $now->timestamp ||
            $end_date < $now->timestamp
        )
        {
            return $this;
        }

        setcookie('popup_offer', $now->timestamp, $now->addMonth()->timestamp, '/');
        $this->view_data['popup'] = $popup;
        return $this;
    }

}
